<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TV Premium | Mais de 10.000 Canais por um Preço Justo!</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #fff;
            background: #0a0a0a;
            overflow-x: hidden;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            max-width: 800px;
            padding: 0 20px;
            z-index: 2;
            position: relative;
            animation: fadeInUp 1s ease-out;
        }

        .hero h1 {
            font-size: clamp(2rem, 5vw, 3.5rem);
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero p {
            font-size: clamp(1rem, 2.5vw, 1.3rem);
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: #fff;
            padding: 18px 40px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
        }

        .btn i {
            margin-right: 10px;
        }

        /* Problem Section - FERIR */
        .problem {
            background: linear-gradient(135deg, #2c1810 0%, #1a1a1a 100%);
            padding: 80px 20px;
            text-align: center;
            position: relative;
        }

        .problem::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="10" cy="10" r="1" fill="rgba(255,107,107,0.1)"/><circle cx="90" cy="90" r="1" fill="rgba(255,107,107,0.1)"/></svg>');
            opacity: 0.3;
        }

        .problem-content {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            margin: 0 auto;
        }

        .problem h2 {
            font-size: clamp(1.8rem, 4vw, 2.5rem);
            color: #ff6b6b;
            margin-bottom: 50px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .problem-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .problem-item {
            background: rgba(255, 107, 107, 0.1);
            border: 1px solid rgba(255, 107, 107, 0.3);
            border-radius: 15px;
            padding: 30px 20px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .problem-item:hover {
            transform: translateY(-5px);
            border-color: rgba(255, 107, 107, 0.5);
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.2);
        }

        .problem-item i {
            font-size: 2.5rem;
            color: #ff6b6b;
            margin-bottom: 20px;
        }

        .problem-item h3 {
            font-size: 1.3rem;
            color: #fff;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .problem-item p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }

        .problem-highlight {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border-radius: 20px;
            padding: 30px;
            margin-top: 40px;
            position: relative;
            overflow: hidden;
        }

        .problem-highlight::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
        }

        .problem-highlight h3 {
            color: #fff;
            font-size: clamp(1.2rem, 3vw, 1.5rem);
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 2;
        }

        /* Solution Section - CURAR */
        .features-section {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            padding: 80px 20px;
            text-align: center;
            position: relative;
        }

        .features-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="25" cy="25" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.3;
        }

        .solution-intro {
            position: relative;
            z-index: 2;
            max-width: 900px;
            margin: 0 auto 60px;
        }

        .features-section h2 {
            font-size: clamp(1.8rem, 4vw, 2.5rem);
            margin-bottom: 30px;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .solution-description {
            font-size: clamp(1.1rem, 2.5vw, 1.3rem);
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .solution-description strong {
            color: #fff;
            font-weight: 700;
            background: rgba(255, 255, 255, 0.1);
            padding: 2px 8px;
            border-radius: 5px;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .feature {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .feature:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .feature-icon {
            font-size: 3rem;
            color: #ff6b6b;
            margin-bottom: 20px;
        }

        .feature h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #fff;
        }

        .feature p {
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .feature-badge {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-block;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .solution-cta {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 40px 30px;
            margin-top: 60px;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 2;
        }

        .solution-cta h3 {
            font-size: clamp(1.3rem, 3vw, 1.8rem);
            color: #fff;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .solution-cta p {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 25px;
        }

        .solution-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            font-size: 1.1rem;
            padding: 18px 40px;
            animation: pulse-glow 2s infinite;
        }

        @keyframes pulse-glow {
            0% {
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            }
            50% {
                box-shadow: 0 12px 35px rgba(255, 107, 107, 0.5);
            }
            100% {
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            }
        }

        /* Stats Section */
        .stats {
            background: #0a0a0a;
            padding: 60px 20px;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }

        .stat {
            padding: 20px;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #ff6b6b;
            display: block;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.8;
            margin-top: 10px;
        }

        /* Pricing Section */
        .pricing-section {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            padding: 80px 20px;
            position: relative;
        }

        .pricing-container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }

        .pricing-section h2 {
            font-size: clamp(2rem, 4vw, 2.8rem);
            margin-bottom: 15px;
            color: #fff;
            font-weight: 700;
        }

        .pricing-subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 50px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .pricing-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .pricing-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            transition: all 0.4s ease;
            position: relative;
        }

        .pricing-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .pricing-card.popular {
            transform: scale(1.05);
            border: 2px solid #ff6b6b;
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.3);
        }

        .pricing-card.popular:hover {
            transform: scale(1.05) translateY(-10px);
        }

        .popular-badge {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: #fff;
            padding: 8px 20px;
            font-size: 0.9rem;
            font-weight: 600;
            text-align: center;
            position: relative;
        }

        .popular-badge i {
            margin-right: 5px;
        }

        .card-header {
            padding: 30px 30px 20px;
            text-align: center;
        }

        .card-header h3 {
            font-size: 1.5rem;
            color: #fff;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .price {
            display: flex;
            align-items: baseline;
            justify-content: center;
            margin-bottom: 10px;
        }

        .currency {
            font-size: 1.2rem;
            color: #ff6b6b;
            font-weight: 600;
        }

        .amount {
            font-size: 3rem;
            color: #fff;
            font-weight: 700;
            margin: 0 5px;
        }

        .period {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .savings {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: #fff;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-block;
        }

        .card-body {
            padding: 0 30px 30px;
        }

        .features-list {
            list-style: none;
            margin-bottom: 30px;
            text-align: left;
        }

        .features-list li {
            padding: 8px 0;
            color: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
        }

        .features-list i {
            color: #00b894;
            margin-right: 10px;
            width: 16px;
        }

        .pricing-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: #fff;
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            display: block;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .pricing-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
            text-decoration: none;
            color: #fff;
        }

        .pricing-btn i {
            margin-right: 8px;
        }

        .pricing-guarantee {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 600px;
            margin: 0 auto;
        }

        .guarantee-content {
            display: flex;
            align-items: center;
            gap: 20px;
            text-align: left;
        }

        .guarantee-content i {
            font-size: 2.5rem;
            color: #00b894;
            flex-shrink: 0;
        }

        .guarantee-content h4 {
            color: #fff;
            margin-bottom: 5px;
            font-size: 1.2rem;
        }

        .guarantee-content p {
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
        }

        /* CTA Section */
        .cta {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            padding: 80px 20px;
            text-align: center;
            position: relative;
        }

        .cta::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="70" r="1" fill="rgba(255,255,255,0.05)"/></svg>');
        }

        .cta-content {
            position: relative;
            z-index: 2;
        }

        .cta h2 {
            font-size: clamp(1.8rem, 4vw, 2.5rem);
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .cta .btn {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            font-size: 1.2rem;
            padding: 20px 50px;
        }

        .cta .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        /* Footer */
        .footer {
            background: #000;
            padding: 40px 20px;
            text-align: center;
            border-top: 1px solid #333;
        }

        .footer p {
            color: #999;
            font-size: 0.9rem;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in {
            animation: fadeInUp 0.8s ease-out;
        }

        /* Floating WhatsApp Button */
        .whatsapp-float {
            position: fixed;
            width: 60px;
            height: 60px;
            bottom: 40px;
            right: 40px;
            background: #25d366;
            color: #fff;
            border-radius: 50px;
            text-align: center;
            font-size: 30px;
            box-shadow: 2px 2px 3px #999;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            animation: pulse 2s infinite;
        }

        .whatsapp-float:hover {
            transform: scale(1.1);
            text-decoration: none;
            color: #fff;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero {
                padding: 40px 20px;
            }

            .features {
                grid-template-columns: 1fr;
            }

            .problem-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .problem-item {
                padding: 25px 15px;
            }

            .solution-cta {
                padding: 30px 20px;
                margin-top: 40px;
            }

            .stats-container {
                grid-template-columns: repeat(2, 1fr);
            }

            .pricing-cards {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .pricing-card.popular {
                transform: none;
            }

            .pricing-card.popular:hover {
                transform: translateY(-10px);
            }

            .guarantee-content {
                flex-direction: column;
                text-align: center;
            }

            .guarantee-content i {
                font-size: 2rem;
            }

            .whatsapp-float {
                width: 50px;
                height: 50px;
                bottom: 20px;
                right: 20px;
                font-size: 24px;
            }
        }

        @media (max-width: 480px) {
            .pricing-section {
                padding: 60px 15px;
            }

            .card-header, .card-body {
                padding: 20px;
            }

            .amount {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1>Você já parou pra pensar quanto tempo e dinheiro você perde com TV a cabo tradicional?</h1>
            <p>Descubra a verdade sobre o que você está pagando e como pode ter muito mais por muito menos!</p>
            <a href="https://wa.me/5521971321407" class="btn" target="_blank">
                <i class="fab fa-whatsapp"></i>
                Quero Descobrir Agora!
            </a>
        </div>
    </section>

    <!-- Problem Section - FERIR -->
    <section class="problem">
        <h2><i class="fas fa-exclamation-triangle"></i> A Dura Realidade da TV Tradicional:</h2>
        <div class="problem-content">
            <div class="problem-grid">
                <div class="problem-item">
                    <i class="fas fa-money-bill-wave"></i>
                    <h3>Mensalidades Altas</h3>
                    <p>Você paga R$ 100, R$ 150 ou até mais por mês em pacotes caros</p>
                </div>
                <div class="problem-item">
                    <i class="fas fa-tv"></i>
                    <h3>Canais Inúteis</h3>
                    <p>Dezenas de canais que você nem assiste, mas paga por eles</p>
                </div>
                <div class="problem-item">
                    <i class="fas fa-wifi"></i>
                    <h3>Travamentos Constantes</h3>
                    <p>Quando mais precisa, o sinal cai ou a imagem trava</p>
                </div>
                <div class="problem-item">
                    <i class="fas fa-lock"></i>
                    <h3>Sem Liberdade</h3>
                    <p>Limitado ao que eles querem te mostrar, sem escolha real</p>
                </div>
                <div class="problem-item">
                    <i class="fas fa-calendar-times"></i>
                    <h3>Filmes Antigos</h3>
                    <p>Sem acesso a lançamentos e conteúdo internacional</p>
                </div>
                <div class="problem-item">
                    <i class="fas fa-ban"></i>
                    <h3>Esportes Limitados</h3>
                    <p>Perde jogos importantes por não ter todos os canais esportivos</p>
                </div>
            </div>
            <div class="problem-highlight">
                <h3>📺 E o pior: você está limitado ao que eles querem te mostrar, sem liberdade de escolher seus conteúdos preferidos!</h3>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
        <div class="stats-container">
            <div class="stat">
                <span class="stat-number">10.000+</span>
                <div class="stat-label">Canais Disponíveis</div>
            </div>
            <div class="stat">
                <span class="stat-number">HD/4K</span>
                <div class="stat-label">Qualidade Premium</div>
            </div>
            <div class="stat">
                <span class="stat-number">24/7</span>
                <div class="stat-label">Suporte Técnico</div>
            </div>
            <div class="stat">
                <span class="stat-number">100%</span>
                <div class="stat-label">Satisfação</div>
            </div>
        </div>
    </section>

    <!-- Solution Section - CURAR -->
    <section class="features-section">
        <div class="solution-intro">
            <h2>💡 Agora imagine ter acesso a mais de 10.000 canais nacionais e internacionais...</h2>
            <p class="solution-description">
                Com filmes, séries, esportes ao vivo, canais adultos, infantis, tudo em alta definição e com estabilidade —
                <strong>tudo isso por um valor muito menor do que você paga hoje!</strong>
            </p>
        </div>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-tv"></i>
                </div>
                <h3>+10.000 Canais</h3>
                <p>Nacionais e internacionais, filmes, séries, esportes, infantis, documentários, adultos e muito mais!</p>
                <div class="feature-badge">✅ Sem fidelidade</div>
            </div>
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <h3>Suporte Rápido</h3>
                <p>Atendimento ágil e eficiente para resolver qualquer problema na hora!</p>
                <div class="feature-badge">✅ Suporte rápido</div>
            </div>
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-gift"></i>
                </div>
                <h3>Teste Grátis</h3>
                <p>Experimente antes de pagar! Teste a qualidade e estabilidade sem compromisso.</p>
                <div class="feature-badge">✅ Teste grátis</div>
            </div>
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-devices"></i>
                </div>
                <h3>Compatível com Tudo</h3>
                <p>Smart TVs, celulares, computadores, TV Box - assista onde e quando quiser!</p>
                <div class="feature-badge">✅ Compatível com tudo</div>
            </div>
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-bolt"></i>
                </div>
                <h3>Acesso Imediato</h3>
                <p>Sem espera, sem burocracia. Ative e comece a assistir em minutos!</p>
                <div class="feature-badge">✅ Acesso imediato</div>
            </div>
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-hd-video"></i>
                </div>
                <h3>Alta Definição</h3>
                <p>Qualidade HD e 4K com estabilidade total, sem travamentos ou quedas de sinal.</p>
                <div class="feature-badge">✅ Qualidade garantida</div>
            </div>
        </div>

        <div class="solution-cta">
            <h3>📲 Quer experimentar sem compromisso?</h3>
            <p>Me chama agora no WhatsApp e te libero um teste gratuito!</p>
            <a href="https://wa.me/5521971321407?text=Olá! Quero experimentar o teste GRATUITO dos canais de TV!" class="btn solution-btn" target="_blank">
                <i class="fab fa-whatsapp"></i>
                Quero Meu Teste Grátis Agora!
            </a>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing-section">
        <div class="pricing-container">
            <h2>Escolha seu Plano</h2>
            <p class="pricing-subtitle">Preços justos para você economizar e ter o melhor entretenimento</p>

            <div class="pricing-cards">
                <!-- Mensal -->
                <div class="pricing-card">
                    <div class="card-header">
                        <h3>Mensal</h3>
                        <div class="price">
                            <span class="currency">R$</span>
                            <span class="amount">35</span>
                            <span class="period">/mês</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <ul class="features-list">
                            <li><i class="fas fa-check"></i> +10.000 Canais</li>
                            <li><i class="fas fa-check"></i> Qualidade HD/4K</li>
                            <li><i class="fas fa-check"></i> Filmes e Séries</li>
                            <li><i class="fas fa-check"></i> Esportes ao Vivo</li>
                            <li><i class="fas fa-check"></i> Suporte 24/7</li>
                            <li><i class="fas fa-check"></i> Sem Contrato</li>
                        </ul>
                        <a href="https://wa.me/5521971321407?text=Olá! Tenho interesse no plano MENSAL de R$ 35,00. Gostaria de mais informações!" class="pricing-btn" target="_blank">
                            <i class="fab fa-whatsapp"></i>
                            Contratar Agora
                        </a>
                    </div>
                </div>

                <!-- Bimestral -->
                <div class="pricing-card popular">
                    <div class="popular-badge">
                        <i class="fas fa-crown"></i>
                        Mais Popular
                    </div>
                    <div class="card-header">
                        <h3>Bimestral</h3>
                        <div class="price">
                            <span class="currency">R$</span>
                            <span class="amount">60</span>
                            <span class="period">/2 meses</span>
                        </div>
                        <div class="savings">Economize R$ 10</div>
                    </div>
                    <div class="card-body">
                        <ul class="features-list">
                            <li><i class="fas fa-check"></i> +10.000 Canais</li>
                            <li><i class="fas fa-check"></i> Qualidade HD/4K</li>
                            <li><i class="fas fa-check"></i> Filmes e Séries</li>
                            <li><i class="fas fa-check"></i> Esportes ao Vivo</li>
                            <li><i class="fas fa-check"></i> Suporte 24/7</li>
                            <li><i class="fas fa-check"></i> Sem Contrato</li>
                            <li><i class="fas fa-star"></i> Suporte Prioritário</li>
                        </ul>
                        <a href="https://wa.me/5521971321407?text=Olá! Tenho interesse no plano BIMESTRAL de R$ 60,00. Gostaria de mais informações!" class="pricing-btn" target="_blank">
                            <i class="fab fa-whatsapp"></i>
                            Contratar Agora
                        </a>
                    </div>
                </div>

                <!-- Trimestral -->
                <div class="pricing-card">
                    <div class="card-header">
                        <h3>Trimestral</h3>
                        <div class="price">
                            <span class="currency">R$</span>
                            <span class="amount">75</span>
                            <span class="period">/3 meses</span>
                        </div>
                        <div class="savings">Economize R$ 30</div>
                    </div>
                    <div class="card-body">
                        <ul class="features-list">
                            <li><i class="fas fa-check"></i> +10.000 Canais</li>
                            <li><i class="fas fa-check"></i> Qualidade HD/4K</li>
                            <li><i class="fas fa-check"></i> Filmes e Séries</li>
                            <li><i class="fas fa-check"></i> Esportes ao Vivo</li>
                            <li><i class="fas fa-check"></i> Suporte 24/7</li>
                            <li><i class="fas fa-check"></i> Sem Contrato</li>
                            <li><i class="fas fa-star"></i> Suporte Prioritário</li>
                            <li><i class="fas fa-gift"></i> Bônus Especiais</li>
                        </ul>
                        <a href="https://wa.me/5521971321407?text=Olá! Tenho interesse no plano TRIMESTRAL de R$ 75,00. Gostaria de mais informações!" class="pricing-btn" target="_blank">
                            <i class="fab fa-whatsapp"></i>
                            Contratar Agora
                        </a>
                    </div>
                </div>
            </div>

            <div class="pricing-guarantee">
                <div class="guarantee-content">
                    <i class="fas fa-shield-alt"></i>
                    <div>
                        <h4>Garantia de Satisfação</h4>
                        <p>Teste grátis disponível • Suporte técnico incluso • Sem taxas ocultas</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
        <div class="cta-content">
            <h2>Garanta agora seu acesso e tenha TV premium com preço justo!</h2>
            <a href="https://wa.me/5521971321407" class="btn" target="_blank">
                <i class="fab fa-whatsapp"></i>
                Chamar no WhatsApp
            </a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <p>© 2025 TV Premium. Todos os direitos reservados. | Desenvolvido por Lauren Xavier</p>
    </footer>

    <!-- Floating WhatsApp Button -->
    <a href="https://wa.me/5521971321407" class="whatsapp-float" target="_blank">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add fade-in animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.feature, .stat, .pricing-card, .problem-item, .solution-cta').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>
