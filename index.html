<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>TV Premium | Mais de 10.000 Canais por um Preço Justo!</title>
    <meta name="description" content="Tenha acesso a +10.000 canais, filmes, séries e esportes em HD por um preço justo. Teste grátis disponível!">
    <meta name="keywords" content="IPTV, canais de TV, streaming, filmes, séries, esportes">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        /* Reset e Base Styles - Mobile First */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            -webkit-text-size-adjust: 100%;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #fff;
            background: #0a0a0a;
            overflow-x: hidden;
            font-size: 16px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Container padrão */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }

        /* Utilitários */
        .text-center { text-align: center; }
        .mb-20 { margin-bottom: 20px; }
        .mb-30 { margin-bottom: 30px; }
        .mb-40 { margin-bottom: 40px; }

        /* Hero Section - Mobile First */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
            overflow: hidden;
            padding: 60px 15px 40px;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 70%, rgba(255,255,255,0.1) 0%, transparent 50%),
                        radial-gradient(circle at 70% 30%, rgba(255,255,255,0.05) 0%, transparent 50%);
            opacity: 0.6;
        }

        .hero-content {
            max-width: 100%;
            width: 100%;
            z-index: 2;
            position: relative;
            animation: fadeInUp 1s ease-out;
        }

        .hero h1 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            line-height: 1.3;
        }

        .hero p {
            font-size: 1rem;
            margin-bottom: 30px;
            opacity: 0.95;
            line-height: 1.5;
            max-width: 90%;
            margin-left: auto;
            margin-right: auto;
        }

        /* Tablets */
        @media (min-width: 768px) {
            .hero {
                padding: 80px 30px 60px;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1.2rem;
                max-width: 80%;
            }
        }

        /* Desktop */
        @media (min-width: 1024px) {
            .hero {
                padding: 100px 40px 80px;
            }

            .hero h1 {
                font-size: 3.2rem;
            }

            .hero p {
                font-size: 1.3rem;
                max-width: 70%;
            }
        }

        /* Botões Responsivos */
        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: #fff;
            padding: 16px 24px;
            border: none;
            border-radius: 50px;
            font-size: 0.95rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            width: 100%;
            max-width: 280px;
            text-align: center;
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
        }

        .btn:hover, .btn:focus {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
            text-decoration: none;
            color: #fff;
        }

        .btn:active {
            transform: translateY(0);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .btn i {
            margin-right: 8px;
            font-size: 1.1em;
        }

        /* Tablets */
        @media (min-width: 768px) {
            .btn {
                padding: 18px 32px;
                font-size: 1rem;
                max-width: 320px;
            }
        }

        /* Desktop */
        @media (min-width: 1024px) {
            .btn {
                padding: 20px 40px;
                font-size: 1.1rem;
                max-width: 350px;
                width: auto;
            }

            .btn:hover {
                transform: translateY(-3px);
                box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
            }
        }

        /* Problem Section - FERIR - Mobile First */
        .problem {
            background: linear-gradient(135deg, #2c1810 0%, #1a1a1a 100%);
            padding: 50px 15px;
            text-align: center;
            position: relative;
        }

        .problem::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(255,107,107,0.1) 0%, transparent 50%);
            opacity: 0.6;
        }

        .problem-content {
            position: relative;
            z-index: 2;
            max-width: 100%;
        }

        .problem h2 {
            font-size: 1.6rem;
            color: #ff6b6b;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            line-height: 1.3;
        }

        .problem-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .problem-item {
            background: rgba(255, 107, 107, 0.1);
            border: 1px solid rgba(255, 107, 107, 0.3);
            border-radius: 15px;
            padding: 25px 20px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .problem-item:hover {
            transform: translateY(-3px);
            border-color: rgba(255, 107, 107, 0.5);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.2);
        }

        .problem-item i {
            font-size: 2rem;
            color: #ff6b6b;
            margin-bottom: 15px;
        }

        .problem-item h3 {
            font-size: 1.1rem;
            color: #fff;
            margin-bottom: 12px;
            font-weight: 600;
        }

        .problem-item p {
            color: rgba(255, 255, 255, 0.85);
            line-height: 1.5;
            font-size: 0.95rem;
        }

        .problem-highlight {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border-radius: 15px;
            padding: 25px 20px;
            margin-top: 30px;
            position: relative;
            overflow: hidden;
        }

        .problem-highlight::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 70%, rgba(255,255,255,0.1) 0%, transparent 50%);
        }

        .problem-highlight h3 {
            color: #fff;
            font-size: 1.1rem;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 2;
            line-height: 1.4;
        }

        /* Tablets */
        @media (min-width: 768px) {
            .problem {
                padding: 60px 30px;
            }

            .problem h2 {
                font-size: 2rem;
                margin-bottom: 40px;
            }

            .problem-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 25px;
                margin-bottom: 40px;
            }

            .problem-item {
                padding: 30px 25px;
            }

            .problem-item i {
                font-size: 2.3rem;
            }

            .problem-item h3 {
                font-size: 1.2rem;
            }

            .problem-item p {
                font-size: 1rem;
            }

            .problem-highlight {
                padding: 30px 25px;
            }

            .problem-highlight h3 {
                font-size: 1.3rem;
            }
        }

        /* Desktop */
        @media (min-width: 1024px) {
            .problem {
                padding: 80px 40px;
            }

            .problem h2 {
                font-size: 2.5rem;
                margin-bottom: 50px;
            }

            .problem-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 30px;
            }

            .problem-item {
                padding: 35px 30px;
            }

            .problem-item i {
                font-size: 2.5rem;
                margin-bottom: 20px;
            }

            .problem-item h3 {
                font-size: 1.3rem;
                margin-bottom: 15px;
            }

            .problem-item p {
                font-size: 1rem;
                line-height: 1.6;
            }

            .problem-highlight {
                padding: 35px 30px;
                margin-top: 40px;
            }

            .problem-highlight h3 {
                font-size: 1.5rem;
            }

            .problem-item:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 30px rgba(255, 107, 107, 0.2);
            }
        }

        /* Solution Section - CURAR - Mobile First */
        .features-section {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            padding: 50px 15px;
            text-align: center;
            position: relative;
        }

        .features-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 70% 30%, rgba(255,255,255,0.1) 0%, transparent 50%);
            opacity: 0.6;
        }

        .solution-intro {
            position: relative;
            z-index: 2;
            max-width: 100%;
            margin: 0 auto 40px;
        }

        .features-section h2 {
            font-size: 1.6rem;
            margin-bottom: 20px;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            line-height: 1.3;
        }

        .solution-description {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.95);
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .solution-description strong {
            color: #fff;
            font-weight: 700;
            background: rgba(255, 255, 255, 0.15);
            padding: 2px 6px;
            border-radius: 4px;
        }

        /* Tablets */
        @media (min-width: 768px) {
            .features-section {
                padding: 60px 30px;
            }

            .solution-intro {
                margin-bottom: 50px;
                max-width: 90%;
            }

            .features-section h2 {
                font-size: 2rem;
                margin-bottom: 25px;
            }

            .solution-description {
                font-size: 1.1rem;
                margin-bottom: 20px;
            }
        }

        /* Desktop */
        @media (min-width: 1024px) {
            .features-section {
                padding: 80px 40px;
            }

            .solution-intro {
                margin-bottom: 60px;
                max-width: 80%;
            }

            .features-section h2 {
                font-size: 2.5rem;
                margin-bottom: 30px;
            }

            .solution-description {
                font-size: 1.3rem;
                line-height: 1.6;
            }
        }

        /* Features Grid - Mobile First */
        .features {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            max-width: 100%;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .feature {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px 20px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.15);
            text-align: center;
        }

        .feature:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 255, 255, 0.25);
        }

        .feature-icon {
            font-size: 2.5rem;
            color: #ff6b6b;
            margin-bottom: 15px;
        }

        .feature h3 {
            font-size: 1.2rem;
            margin-bottom: 12px;
            color: #fff;
            font-weight: 600;
        }

        .feature p {
            opacity: 0.9;
            line-height: 1.5;
            margin-bottom: 15px;
            font-size: 0.95rem;
        }

        .feature-badge {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 600;
            display: inline-block;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* Tablets */
        @media (min-width: 768px) {
            .features {
                grid-template-columns: repeat(2, 1fr);
                gap: 25px;
            }

            .feature {
                padding: 30px 25px;
            }

            .feature-icon {
                font-size: 2.8rem;
                margin-bottom: 18px;
            }

            .feature h3 {
                font-size: 1.3rem;
                margin-bottom: 15px;
            }

            .feature p {
                font-size: 1rem;
            }

            .feature-badge {
                padding: 8px 15px;
                font-size: 0.9rem;
            }
        }

        /* Desktop */
        @media (min-width: 1024px) {
            .features {
                grid-template-columns: repeat(3, 1fr);
                gap: 30px;
                max-width: 1200px;
            }

            .feature {
                padding: 35px 30px;
                border-radius: 20px;
            }

            .feature:hover {
                transform: translateY(-8px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            }

            .feature-icon {
                font-size: 3rem;
                margin-bottom: 20px;
            }

            .feature h3 {
                font-size: 1.5rem;
            }

            .feature p {
                line-height: 1.6;
            }
        }

        /* Solution CTA - Mobile First */
        .solution-cta {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px 20px;
            margin-top: 40px;
            max-width: 100%;
            margin-left: auto;
            margin-right: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 2;
        }

        .solution-cta h3 {
            font-size: 1.3rem;
            color: #fff;
            margin-bottom: 12px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            line-height: 1.3;
        }

        .solution-cta p {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 20px;
            line-height: 1.4;
        }

        .solution-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            font-size: 1rem;
            padding: 16px 24px;
            animation: pulse-glow 2s infinite;
            width: 100%;
            max-width: 300px;
        }

        @keyframes pulse-glow {
            0% {
                box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
            }
            50% {
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.5);
            }
            100% {
                box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
            }
        }

        /* Tablets */
        @media (min-width: 768px) {
            .solution-cta {
                padding: 35px 25px;
                margin-top: 50px;
                max-width: 600px;
            }

            .solution-cta h3 {
                font-size: 1.5rem;
                margin-bottom: 15px;
            }

            .solution-cta p {
                font-size: 1.1rem;
                margin-bottom: 25px;
            }

            .solution-btn {
                font-size: 1.05rem;
                padding: 17px 32px;
                max-width: 320px;
            }
        }

        /* Desktop */
        @media (min-width: 1024px) {
            .solution-cta {
                padding: 40px 30px;
                margin-top: 60px;
                max-width: 700px;
                border-radius: 25px;
            }

            .solution-cta h3 {
                font-size: 1.8rem;
            }

            .solution-btn {
                font-size: 1.1rem;
                padding: 18px 40px;
                max-width: 350px;
                width: auto;
            }

            @keyframes pulse-glow {
                0% {
                    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
                }
                50% {
                    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.5);
                }
                100% {
                    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
                }
            }
        }

        /* Stats Section - Mobile First */
        .stats {
            background: #0a0a0a;
            padding: 40px 15px;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            max-width: 100%;
            margin: 0 auto;
            text-align: center;
        }

        .stat {
            padding: 20px 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .stat:hover {
            transform: translateY(-3px);
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 107, 107, 0.3);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #ff6b6b;
            display: block;
            line-height: 1.2;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 8px;
            line-height: 1.3;
        }

        /* Tablets */
        @media (min-width: 768px) {
            .stats {
                padding: 50px 30px;
            }

            .stats-container {
                grid-template-columns: repeat(4, 1fr);
                gap: 25px;
                max-width: 900px;
            }

            .stat {
                padding: 25px 15px;
            }

            .stat-number {
                font-size: 2.5rem;
            }

            .stat-label {
                font-size: 1rem;
                margin-top: 10px;
            }
        }

        /* Desktop */
        @media (min-width: 1024px) {
            .stats {
                padding: 60px 40px;
            }

            .stats-container {
                gap: 30px;
                max-width: 1000px;
            }

            .stat {
                padding: 30px 20px;
            }

            .stat-number {
                font-size: 3rem;
            }

            .stat-label {
                font-size: 1.1rem;
            }
        }

        /* Pricing Section - Mobile First */
        .pricing-section {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            padding: 50px 15px;
            position: relative;
        }

        .pricing-container {
            max-width: 100%;
            margin: 0 auto;
            text-align: center;
        }

        .pricing-section h2 {
            font-size: 1.8rem;
            margin-bottom: 12px;
            color: #fff;
            font-weight: 700;
            line-height: 1.3;
        }

        .pricing-subtitle {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.85);
            margin-bottom: 40px;
            max-width: 100%;
            line-height: 1.4;
        }

        .pricing-cards {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-bottom: 40px;
        }

        /* Tablets */
        @media (min-width: 768px) {
            .pricing-section {
                padding: 60px 30px;
            }

            .pricing-container {
                max-width: 900px;
            }

            .pricing-section h2 {
                font-size: 2.2rem;
                margin-bottom: 15px;
            }

            .pricing-subtitle {
                font-size: 1.1rem;
                margin-bottom: 45px;
                max-width: 80%;
            }

            .pricing-cards {
                grid-template-columns: 1fr;
                gap: 25px;
                margin-bottom: 45px;
            }
        }

        /* Desktop */
        @media (min-width: 1024px) {
            .pricing-section {
                padding: 80px 40px;
            }

            .pricing-container {
                max-width: 1200px;
            }

            .pricing-section h2 {
                font-size: 2.8rem;
            }

            .pricing-subtitle {
                font-size: 1.2rem;
                margin-bottom: 50px;
                max-width: 70%;
            }

            .pricing-cards {
                grid-template-columns: repeat(3, 1fr);
                gap: 30px;
            }
        }

        /* Pricing Cards - Mobile First */
        .pricing-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }

        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .pricing-card.popular {
            border: 2px solid #ff6b6b;
            box-shadow: 0 10px 25px rgba(255, 107, 107, 0.3);
        }

        .pricing-card.popular:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
        }

        /* Tablets */
        @media (min-width: 768px) {
            .pricing-card {
                border-radius: 18px;
            }

            .pricing-card:hover {
                transform: translateY(-8px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);
            }

            .pricing-card.popular:hover {
                transform: translateY(-8px);
                box-shadow: 0 20px 45px rgba(255, 107, 107, 0.4);
            }
        }

        /* Desktop */
        @media (min-width: 1024px) {
            .pricing-card {
                border-radius: 20px;
            }

            .pricing-card:hover {
                transform: translateY(-10px);
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            }

            .pricing-card.popular {
                transform: scale(1.05);
            }

            .pricing-card.popular:hover {
                transform: scale(1.05) translateY(-10px);
            }
        }

        /* Pricing Card Elements - Mobile First */
        .popular-badge {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: #fff;
            padding: 6px 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-align: center;
            position: relative;
        }

        .popular-badge i {
            margin-right: 4px;
        }

        .card-header {
            padding: 25px 20px 15px;
            text-align: center;
        }

        .card-header h3 {
            font-size: 1.3rem;
            color: #fff;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .price {
            display: flex;
            align-items: baseline;
            justify-content: center;
            margin-bottom: 8px;
        }

        .currency {
            font-size: 1rem;
            color: #ff6b6b;
            font-weight: 600;
        }

        .amount {
            font-size: 2.5rem;
            color: #fff;
            font-weight: 700;
            margin: 0 4px;
        }

        .period {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .savings {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: #fff;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            display: inline-block;
        }

        /* Tablets */
        @media (min-width: 768px) {
            .popular-badge {
                padding: 7px 18px;
                font-size: 0.85rem;
            }

            .card-header {
                padding: 28px 25px 18px;
            }

            .card-header h3 {
                font-size: 1.4rem;
                margin-bottom: 18px;
            }

            .currency {
                font-size: 1.1rem;
            }

            .amount {
                font-size: 2.8rem;
            }

            .period {
                font-size: 0.95rem;
            }

            .savings {
                padding: 5px 14px;
                font-size: 0.85rem;
            }
        }

        /* Desktop */
        @media (min-width: 1024px) {
            .popular-badge {
                padding: 8px 20px;
                font-size: 0.9rem;
            }

            .card-header {
                padding: 30px 30px 20px;
            }

            .card-header h3 {
                font-size: 1.5rem;
                margin-bottom: 20px;
            }

            .price {
                margin-bottom: 10px;
            }

            .currency {
                font-size: 1.2rem;
            }

            .amount {
                font-size: 3rem;
                margin: 0 5px;
            }

            .period {
                font-size: 1rem;
            }

            .savings {
                padding: 5px 15px;
                border-radius: 20px;
                font-size: 0.9rem;
            }
        }

        /* Card Body - Mobile First */
        .card-body {
            padding: 0 20px 25px;
        }

        .features-list {
            list-style: none;
            margin-bottom: 25px;
            text-align: left;
        }

        .features-list li {
            padding: 6px 0;
            color: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            font-size: 0.9rem;
        }

        .features-list i {
            color: #00b894;
            margin-right: 8px;
            width: 14px;
            font-size: 0.9rem;
        }

        .pricing-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: #fff;
            padding: 14px 24px;
            border: none;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 600;
            text-decoration: none;
            display: block;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            width: 100%;
            text-align: center;
        }

        .pricing-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
            text-decoration: none;
            color: #fff;
        }

        .pricing-btn i {
            margin-right: 6px;
        }

        /* Tablets */
        @media (min-width: 768px) {
            .card-body {
                padding: 0 25px 28px;
            }

            .features-list {
                margin-bottom: 28px;
            }

            .features-list li {
                padding: 7px 0;
                font-size: 0.95rem;
            }

            .features-list i {
                margin-right: 9px;
                width: 15px;
            }

            .pricing-btn {
                padding: 15px 28px;
                font-size: 0.95rem;
            }

            .pricing-btn i {
                margin-right: 7px;
            }
        }

        /* Desktop */
        @media (min-width: 1024px) {
            .card-body {
                padding: 0 30px 30px;
            }

            .features-list {
                margin-bottom: 30px;
            }

            .features-list li {
                padding: 8px 0;
                font-size: 1rem;
            }

            .features-list i {
                margin-right: 10px;
                width: 16px;
                font-size: 1rem;
            }

            .pricing-btn {
                padding: 15px 30px;
                font-size: 1rem;
            }

            .pricing-btn i {
                margin-right: 8px;
            }
        }

        /* Pricing Guarantee - Mobile First */
        .pricing-guarantee {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 100%;
            margin: 0 auto;
        }

        .guarantee-content {
            display: flex;
            align-items: center;
            gap: 15px;
            text-align: left;
        }

        .guarantee-content i {
            font-size: 2rem;
            color: #00b894;
            flex-shrink: 0;
        }

        .guarantee-content h4 {
            color: #fff;
            margin-bottom: 4px;
            font-size: 1rem;
            font-weight: 600;
        }

        .guarantee-content p {
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        /* Tablets */
        @media (min-width: 768px) {
            .pricing-guarantee {
                padding: 22px 20px;
                max-width: 500px;
            }

            .guarantee-content {
                gap: 18px;
            }

            .guarantee-content i {
                font-size: 2.3rem;
            }

            .guarantee-content h4 {
                font-size: 1.1rem;
            }

            .guarantee-content p {
                font-size: 0.95rem;
            }
        }

        /* Desktop */
        @media (min-width: 1024px) {
            .pricing-guarantee {
                border-radius: 15px;
                padding: 25px;
                max-width: 600px;
            }

            .guarantee-content {
                gap: 20px;
            }

            .guarantee-content i {
                font-size: 2.5rem;
            }

            .guarantee-content h4 {
                font-size: 1.2rem;
                margin-bottom: 5px;
            }

            .guarantee-content p {
                font-size: 1rem;
            }
        }

        /* CTA Section - Mobile First */
        .cta {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            padding: 50px 15px;
            text-align: center;
            position: relative;
        }

        .cta::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 40% 60%, rgba(255,255,255,0.1) 0%, transparent 50%);
            opacity: 0.6;
        }

        .cta-content {
            position: relative;
            z-index: 2;
            max-width: 100%;
        }

        .cta h2 {
            font-size: 1.6rem;
            margin-bottom: 25px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            line-height: 1.3;
        }

        .cta .btn {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            font-size: 1rem;
            padding: 16px 24px;
            max-width: 280px;
        }

        .cta .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        /* Tablets */
        @media (min-width: 768px) {
            .cta {
                padding: 60px 30px;
            }

            .cta h2 {
                font-size: 2rem;
                margin-bottom: 28px;
            }

            .cta .btn {
                font-size: 1.1rem;
                padding: 18px 32px;
                max-width: 320px;
            }
        }

        /* Desktop */
        @media (min-width: 1024px) {
            .cta {
                padding: 80px 40px;
            }

            .cta h2 {
                font-size: 2.5rem;
                margin-bottom: 30px;
            }

            .cta .btn {
                font-size: 1.2rem;
                padding: 20px 50px;
                max-width: 400px;
                width: auto;
            }
        }

        /* Footer - Mobile First */
        .footer {
            background: #000;
            padding: 30px 15px;
            text-align: center;
            border-top: 1px solid #333;
        }

        .footer p {
            color: #999;
            font-size: 0.8rem;
            line-height: 1.4;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Floating WhatsApp Button - Mobile First */
        .whatsapp-float {
            position: fixed;
            width: 50px;
            height: 50px;
            bottom: 20px;
            right: 20px;
            background: #25d366;
            color: #fff;
            border-radius: 50px;
            text-align: center;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            animation: pulse 2s infinite;
            text-decoration: none;
            -webkit-tap-highlight-color: transparent;
        }

        .whatsapp-float:hover, .whatsapp-float:focus {
            transform: scale(1.05);
            text-decoration: none;
            color: #fff;
            box-shadow: 0 6px 16px rgba(37, 211, 102, 0.5);
        }

        .whatsapp-float:active {
            transform: scale(0.95);
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4), 0 0 0 0 rgba(37, 211, 102, 0.7);
            }
            70% {
                box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4), 0 0 0 8px rgba(37, 211, 102, 0);
            }
            100% {
                box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4), 0 0 0 0 rgba(37, 211, 102, 0);
            }
        }

        /* Tablets */
        @media (min-width: 768px) {
            .footer {
                padding: 35px 30px;
            }

            .footer p {
                font-size: 0.85rem;
            }

            .whatsapp-float {
                width: 55px;
                height: 55px;
                bottom: 30px;
                right: 30px;
                font-size: 26px;
            }

            .whatsapp-float:hover {
                transform: scale(1.08);
            }
        }

        /* Desktop */
        @media (min-width: 1024px) {
            .footer {
                padding: 40px 40px;
            }

            .footer p {
                font-size: 0.9rem;
            }

            .whatsapp-float {
                width: 60px;
                height: 60px;
                bottom: 40px;
                right: 40px;
                font-size: 30px;
            }

            .whatsapp-float:hover {
                transform: scale(1.1);
            }

            @keyframes pulse {
                0% {
                    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4), 0 0 0 0 rgba(37, 211, 102, 0.7);
                }
                70% {
                    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4), 0 0 0 10px rgba(37, 211, 102, 0);
                }
                100% {
                    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4), 0 0 0 0 rgba(37, 211, 102, 0);
                }
            }
        }

        /* Ajustes Finais e Otimizações */

        /* Melhorar performance em dispositivos móveis */
        * {
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            -webkit-perspective: 1000;
            perspective: 1000;
        }

        /* Otimizar scrolling em iOS */
        body {
            -webkit-overflow-scrolling: touch;
        }

        /* Melhorar legibilidade em telas pequenas */
        @media (max-width: 480px) {
            .hero h1 {
                font-size: 1.6rem;
            }

            .hero p {
                font-size: 0.95rem;
            }

            .problem h2 {
                font-size: 1.4rem;
            }

            .features-section h2 {
                font-size: 1.4rem;
            }

            .cta h2 {
                font-size: 1.4rem;
            }

            .btn {
                padding: 14px 20px;
                font-size: 0.9rem;
            }

            .whatsapp-float {
                width: 45px;
                height: 45px;
                font-size: 22px;
                bottom: 15px;
                right: 15px;
            }
        }

        /* Melhorar contraste para acessibilidade */
        @media (prefers-contrast: high) {
            .problem-item, .feature, .pricing-card {
                border-width: 2px;
            }

            .btn {
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        }

        /* Reduzir animações para usuários que preferem menos movimento */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }

            .whatsapp-float {
                animation: none;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1>Você já parou pra pensar quanto tempo e dinheiro você perde com TV a cabo tradicional?</h1>
            <p>Descubra a verdade sobre o que você está pagando e como pode ter muito mais por muito menos!</p>
            <a href="https://wa.me/5521971321407" class="btn" target="_blank">
                <i class="fab fa-whatsapp"></i>
                Quero Descobrir Agora!
            </a>
        </div>
    </section>

    <!-- Problem Section - FERIR -->
    <section class="problem">
        <h2><i class="fas fa-exclamation-triangle"></i> A Dura Realidade da TV Tradicional:</h2>
        <div class="problem-content">
            <div class="problem-grid">
                <div class="problem-item">
                    <i class="fas fa-money-bill-wave"></i>
                    <h3>Mensalidades Altas</h3>
                    <p>Você paga R$ 100, R$ 150 ou até mais por mês em pacotes caros</p>
                </div>
                <div class="problem-item">
                    <i class="fas fa-tv"></i>
                    <h3>Canais Inúteis</h3>
                    <p>Dezenas de canais que você nem assiste, mas paga por eles</p>
                </div>
                <div class="problem-item">
                    <i class="fas fa-wifi"></i>
                    <h3>Travamentos Constantes</h3>
                    <p>Quando mais precisa, o sinal cai ou a imagem trava</p>
                </div>
                <div class="problem-item">
                    <i class="fas fa-lock"></i>
                    <h3>Sem Liberdade</h3>
                    <p>Limitado ao que eles querem te mostrar, sem escolha real</p>
                </div>
                <div class="problem-item">
                    <i class="fas fa-calendar-times"></i>
                    <h3>Filmes Antigos</h3>
                    <p>Sem acesso a lançamentos e conteúdo internacional</p>
                </div>
                <div class="problem-item">
                    <i class="fas fa-ban"></i>
                    <h3>Esportes Limitados</h3>
                    <p>Perde jogos importantes por não ter todos os canais esportivos</p>
                </div>
            </div>
            <div class="problem-highlight">
                <h3>📺 E o pior: você está limitado ao que eles querem te mostrar, sem liberdade de escolher seus conteúdos preferidos!</h3>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
        <div class="stats-container">
            <div class="stat">
                <span class="stat-number">10.000+</span>
                <div class="stat-label">Canais Disponíveis</div>
            </div>
            <div class="stat">
                <span class="stat-number">HD/4K</span>
                <div class="stat-label">Qualidade Premium</div>
            </div>
            <div class="stat">
                <span class="stat-number">24/7</span>
                <div class="stat-label">Suporte Técnico</div>
            </div>
            <div class="stat">
                <span class="stat-number">100%</span>
                <div class="stat-label">Satisfação</div>
            </div>
        </div>
    </section>

    <!-- Solution Section - CURAR -->
    <section class="features-section">
        <div class="solution-intro">
            <h2>💡 Agora imagine ter acesso a mais de 10.000 canais nacionais e internacionais...</h2>
            <p class="solution-description">
                Com filmes, séries, esportes ao vivo, canais adultos, infantis, tudo em alta definição e com estabilidade —
                <strong>tudo isso por um valor muito menor do que você paga hoje!</strong>
            </p>
        </div>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-tv"></i>
                </div>
                <h3>+10.000 Canais</h3>
                <p>Nacionais e internacionais, filmes, séries, esportes, infantis, documentários, adultos e muito mais!</p>
                <div class="feature-badge">✅ Sem fidelidade</div>
            </div>
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <h3>Suporte Rápido</h3>
                <p>Atendimento ágil e eficiente para resolver qualquer problema na hora!</p>
                <div class="feature-badge">✅ Suporte rápido</div>
            </div>
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-gift"></i>
                </div>
                <h3>Teste Grátis</h3>
                <p>Experimente antes de pagar! Teste a qualidade e estabilidade sem compromisso.</p>
                <div class="feature-badge">✅ Teste grátis</div>
            </div>
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-devices"></i>
                </div>
                <h3>Compatível com Tudo</h3>
                <p>Smart TVs, celulares, computadores, TV Box - assista onde e quando quiser!</p>
                <div class="feature-badge">✅ Compatível com tudo</div>
            </div>
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-bolt"></i>
                </div>
                <h3>Acesso Imediato</h3>
                <p>Sem espera, sem burocracia. Ative e comece a assistir em minutos!</p>
                <div class="feature-badge">✅ Acesso imediato</div>
            </div>
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-hd-video"></i>
                </div>
                <h3>Alta Definição</h3>
                <p>Qualidade HD e 4K com estabilidade total, sem travamentos ou quedas de sinal.</p>
                <div class="feature-badge">✅ Qualidade garantida</div>
            </div>
        </div>

        <div class="solution-cta">
            <h3>📲 Quer experimentar sem compromisso?</h3>
            <p>Me chama agora no WhatsApp e te libero um teste gratuito!</p>
            <a href="https://wa.me/5521971321407?text=Olá! Quero experimentar o teste GRATUITO dos canais de TV!" class="btn solution-btn" target="_blank">
                <i class="fab fa-whatsapp"></i>
                Quero Meu Teste Grátis Agora!
            </a>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing-section">
        <div class="pricing-container">
            <h2>Escolha seu Plano</h2>
            <p class="pricing-subtitle">Preços justos para você economizar e ter o melhor entretenimento</p>

            <div class="pricing-cards">
                <!-- Mensal -->
                <div class="pricing-card">
                    <div class="card-header">
                        <h3>Mensal</h3>
                        <div class="price">
                            <span class="currency">R$</span>
                            <span class="amount">35</span>
                            <span class="period">/mês</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <ul class="features-list">
                            <li><i class="fas fa-check"></i> +10.000 Canais</li>
                            <li><i class="fas fa-check"></i> Qualidade HD/4K</li>
                            <li><i class="fas fa-check"></i> Filmes e Séries</li>
                            <li><i class="fas fa-check"></i> Esportes ao Vivo</li>
                            <li><i class="fas fa-check"></i> Suporte 24/7</li>
                            <li><i class="fas fa-check"></i> Sem Contrato</li>
                        </ul>
                        <a href="https://wa.me/5521971321407?text=Olá! Tenho interesse no plano MENSAL de R$ 35,00. Gostaria de mais informações!" class="pricing-btn" target="_blank">
                            <i class="fab fa-whatsapp"></i>
                            Contratar Agora
                        </a>
                    </div>
                </div>

                <!-- Bimestral -->
                <div class="pricing-card popular">
                    <div class="popular-badge">
                        <i class="fas fa-crown"></i>
                        Mais Popular
                    </div>
                    <div class="card-header">
                        <h3>Bimestral</h3>
                        <div class="price">
                            <span class="currency">R$</span>
                            <span class="amount">60</span>
                            <span class="period">/2 meses</span>
                        </div>
                        <div class="savings">Economize R$ 10</div>
                    </div>
                    <div class="card-body">
                        <ul class="features-list">
                            <li><i class="fas fa-check"></i> +10.000 Canais</li>
                            <li><i class="fas fa-check"></i> Qualidade HD/4K</li>
                            <li><i class="fas fa-check"></i> Filmes e Séries</li>
                            <li><i class="fas fa-check"></i> Esportes ao Vivo</li>
                            <li><i class="fas fa-check"></i> Suporte 24/7</li>
                            <li><i class="fas fa-check"></i> Sem Contrato</li>
                            <li><i class="fas fa-star"></i> Suporte Prioritário</li>
                        </ul>
                        <a href="https://wa.me/5521971321407?text=Olá! Tenho interesse no plano BIMESTRAL de R$ 60,00. Gostaria de mais informações!" class="pricing-btn" target="_blank">
                            <i class="fab fa-whatsapp"></i>
                            Contratar Agora
                        </a>
                    </div>
                </div>

                <!-- Trimestral -->
                <div class="pricing-card">
                    <div class="card-header">
                        <h3>Trimestral</h3>
                        <div class="price">
                            <span class="currency">R$</span>
                            <span class="amount">75</span>
                            <span class="period">/3 meses</span>
                        </div>
                        <div class="savings">Economize R$ 30</div>
                    </div>
                    <div class="card-body">
                        <ul class="features-list">
                            <li><i class="fas fa-check"></i> +10.000 Canais</li>
                            <li><i class="fas fa-check"></i> Qualidade HD/4K</li>
                            <li><i class="fas fa-check"></i> Filmes e Séries</li>
                            <li><i class="fas fa-check"></i> Esportes ao Vivo</li>
                            <li><i class="fas fa-check"></i> Suporte 24/7</li>
                            <li><i class="fas fa-check"></i> Sem Contrato</li>
                            <li><i class="fas fa-star"></i> Suporte Prioritário</li>
                            <li><i class="fas fa-gift"></i> Bônus Especiais</li>
                        </ul>
                        <a href="https://wa.me/5521971321407?text=Olá! Tenho interesse no plano TRIMESTRAL de R$ 75,00. Gostaria de mais informações!" class="pricing-btn" target="_blank">
                            <i class="fab fa-whatsapp"></i>
                            Contratar Agora
                        </a>
                    </div>
                </div>
            </div>

            <div class="pricing-guarantee">
                <div class="guarantee-content">
                    <i class="fas fa-shield-alt"></i>
                    <div>
                        <h4>Garantia de Satisfação</h4>
                        <p>Teste grátis disponível • Suporte técnico incluso • Sem taxas ocultas</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
        <div class="cta-content">
            <h2>Garanta agora seu acesso e tenha TV premium com preço justo!</h2>
            <a href="https://wa.me/5521971321407" class="btn" target="_blank">
                <i class="fab fa-whatsapp"></i>
                Chamar no WhatsApp
            </a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <p>© 2025 TV Premium. Todos os direitos reservados. | Desenvolvido por Lauren Xavier</p>
    </footer>

    <!-- Floating WhatsApp Button -->
    <a href="https://wa.me/5521971321407" class="whatsapp-float" target="_blank">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script>
        // Otimizações para dispositivos móveis
        document.addEventListener('DOMContentLoaded', function() {
            // Smooth scrolling para links âncora
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Intersection Observer otimizado para mobile
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -30px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in');
                        // Parar de observar após animação para melhor performance
                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            // Observar elementos com delay para melhor performance inicial
            setTimeout(() => {
                document.querySelectorAll('.feature, .stat, .pricing-card, .problem-item, .solution-cta').forEach(el => {
                    observer.observe(el);
                });
            }, 100);

            // Otimizar cliques em botões para mobile
            document.querySelectorAll('.btn, .pricing-btn, .whatsapp-float').forEach(btn => {
                btn.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                }, { passive: true });

                btn.addEventListener('touchend', function() {
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                }, { passive: true });
            });

            // Preload crítico para WhatsApp
            const whatsappLink = document.querySelector('.whatsapp-float');
            if (whatsappLink) {
                whatsappLink.addEventListener('click', function(e) {
                    // Adicionar pequeno delay para feedback visual
                    e.preventDefault();
                    setTimeout(() => {
                        window.open(this.href, '_blank');
                    }, 100);
                });
            }

            // Otimizar scroll performance
            let ticking = false;
            function updateScrollEffects() {
                // Adicionar efeitos de scroll se necessário
                ticking = false;
            }

            window.addEventListener('scroll', function() {
                if (!ticking) {
                    requestAnimationFrame(updateScrollEffects);
                    ticking = true;
                }
            }, { passive: true });
        });

        // Service Worker para cache (opcional)
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                // Registrar service worker se disponível
            });
        }
    </script>
</body>
</html>
